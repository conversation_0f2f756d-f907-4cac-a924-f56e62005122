package com.sankuai.algoplatform.waimaimatch.waimai.match.application.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.request.PoiInfo;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.request.ReqInfo;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.response.MatchRecord;
import com.sankuai.algoplatform.waimaimatch.waimai.match.api.response.Result;
import com.sankuai.algoplatform.waimaimatch.waimai.match.application.service.PoiMatchService;
import com.sankuai.algoplatform.waimaimatch.waimai.match.application.tair.TairClient;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictResponse;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictService;
import com.sankuai.algoplatform.waimaimatch.waimai.match.application.es.EsClient;
import com.sankuai.meituan.poros.client.PorosApiClient;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch._types.FieldValue;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PoiMatchServiceImpl implements PoiMatchService {
    @MdpThriftClient(remoteAppKey = "com.sankuai.algoplatform.predictor", remoteServerPort = 9001, timeout = 60000)
    private TPredictService.Iface predictService;

    @Autowired
    private TairClient tairClient;

    @Override
    public Result matchPoi(ReqInfo reqInfo) {
        // 基本健壮性校验
        if (reqInfo == null || reqInfo.getPoiInfos() == null || reqInfo.getPoiInfos().isEmpty()) {
            Result result = new Result();
            result.setReqId(reqInfo != null && reqInfo.getReqId() != null ? reqInfo.getReqId() : 0L);
            result.setSuccess(true);
            result.setMessage("empty poiInfos");
            result.setMatchRecords(Collections.emptyList());
            return result;
        }

        List<PoiInfo> poiInfos = reqInfo.getPoiInfos();
        // 维持按输入顺序输出
        Map<Integer, MatchRecord> finalByTempId = new LinkedHashMap<>();

        // 1. Tair 历史/即时匹配：按要求取 storeId/eid 拼接 match_result_ 前缀批量查询
        Map<String, PoiInfo> id2Poi = new HashMap<>();
        List<String> tairKeys = new ArrayList<>();
        for (PoiInfo p : poiInfos) {
            String id = firstNonBlank(p.getStoreId(), p.getEid());
            if (!isBlank(id)) {
                tairKeys.add(id);
                id2Poi.put(id, p);
            }
        }
        Set<Integer> handledTempIds = new HashSet<>();
        if (!tairKeys.isEmpty()) {
            Map<String, String> cached = safeBatchGet("match_result_", tairKeys);
            for (Map.Entry<String, String> e : cached.entrySet()) {
                PoiInfo poi = id2Poi.get(e.getKey());
                if (poi == null) { continue; }
                Integer tempId = poi.getTempPoiId();
                if (tempId == null) { continue; }
                String json = e.getValue();
                String matchId = extractMatchId(json); // 从缓存JSON中尽力解析 matchId / poiId
                if (isBlank(matchId)) { continue; }
                MatchRecord rec = new MatchRecord();
                rec.setTempPoiId(tempId);
                rec.setMatchId(matchId);
                rec.setMatchType(2); // 按需求：Tair 命中，matchType=2
                finalByTempId.put(tempId, rec);
                handledTempIds.add(tempId);
            }
        }

        // 2. ES 精确匹配（仅对未命中的且 storeId 非空的）
        List<PoiInfo> toEs = poiInfos.stream()
                .filter(p -> p.getTempPoiId() != null && !handledTempIds.contains(p.getTempPoiId()))
                .filter(p -> !isBlank(p.getStoreId()))
                .collect(Collectors.toList());
        if (!toEs.isEmpty()) {
            Map<String, String> esMatches = findExactMatchFromEs(toEs); // key: storeId, val: poi_id
            for (PoiInfo p : toEs) {
                String poiId = esMatches.get(p.getStoreId());
                if (!isBlank(poiId)) {
                    Integer tempId = p.getTempPoiId();
                    MatchRecord rec = new MatchRecord();
                    rec.setTempPoiId(tempId);
                    rec.setMatchId(poiId);
                    rec.setMatchType(1); // 按需求：ES 精确匹配，matchType=1
                    finalByTempId.put(tempId, rec);
                    handledTempIds.add(tempId);
                }
            }
        }

        // 3. 经纬度判空：对仍未命中的，如果经纬度均为空/缺失，则标记为 -1
        List<PoiInfo> remainAfterEs = poiInfos.stream()
                .filter(p -> p.getTempPoiId() != null && !handledTempIds.contains(p.getTempPoiId()))
                .collect(Collectors.toList());
        List<PoiInfo> toPredict = new ArrayList<>();
        for (PoiInfo p : remainAfterEs) {
            boolean noCoords = allEmpty(p.getPoiLat(), p.getPoiLon()) && allEmpty(p.getSdLat(), p.getSdLon());
            if (noCoords) {
                Integer tempId = p.getTempPoiId();
                MatchRecord rec = new MatchRecord();
                rec.setTempPoiId(tempId);
                rec.setMatchType(-1); // 按需求：无坐标，matchType=-1
                finalByTempId.put(tempId, rec);
                handledTempIds.add(tempId);
            } else {
                toPredict.add(p); // 交给模型
            }
        }

        // 4. 组装剩余未命中数据的 poiFields，并调用 predictor
        boolean predictOk = true;
        String predictMsg = "ok";
        if (!toPredict.isEmpty()) {
            List<Map<String, Object>> poiFields = toPredict.stream()
                    .map(this::poiInfoToMap)
                    .collect(Collectors.toList());
            Map<String, Object> extra = buildExtra(reqInfo);
            try {
                TPredictResponse resp = doPredictReflect("poi_match", poiFields, extra);
                // 5. 解析 TPredictResponse：code==0 则成功，读取 data.result(List<Map>) 和 extra
                Map<String, Object> parsed = parsePredictResponse(resp);
                int code = (int) parsed.getOrDefault("code", -1);
                predictOk = (code == 0);
                predictMsg = String.valueOf(parsed.getOrDefault("message", ""));
                if (predictOk) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> resultList = (List<Map<String, Object>>) parsed.getOrDefault("result", Collections.emptyList());
                    // 将模型结果填充到 finalByTempId（不覆盖已有的 1/2/-1）
                    for (Map<String, Object> m : resultList) {
                        Integer tempId = asInt(m.get("tempPoiId"));
                        if (tempId == null) { continue; }
                        if (finalByTempId.containsKey(tempId)) { continue; }
                        String matchId = asStr(firstNonNull(m.get("matchId"), m.get("poi_id")));
                        Double score = asDouble(m.get("score"));
                        String newId = asStr(m.get("newId"));
                        Integer matchType = asInt(m.get("matchType"));
                        if (matchType == null) { matchType = 2; } // 模型匹配默认按即时匹配
                        MatchRecord rec = new MatchRecord();
                        rec.setTempPoiId(tempId);
                        rec.setMatchId(matchId);
                        rec.setScore(score);
                        rec.setNewId(newId);
                        rec.setMatchType(matchType);
                        finalByTempId.put(tempId, rec);
                        handledTempIds.add(tempId);
                    }
                }
            } catch (Exception ex) {
                predictOk = false;
                predictMsg = "predict error: " + ex.getMessage();
            }
        }

        // 汇总为按输入顺序的列表；未出现的置为不匹配（0）
        List<MatchRecord> records = new ArrayList<>(poiInfos.size());
        for (PoiInfo p : poiInfos) {
            Integer tempId = p.getTempPoiId();
            if (tempId == null) { continue; }
            MatchRecord rec = finalByTempId.get(tempId);
            if (rec == null) {
                rec = new MatchRecord();
                rec.setTempPoiId(tempId);
                rec.setMatchType(0); // 不匹配
            }
            records.add(rec);
        }

        Result out = new Result();
        out.setReqId(reqInfo.getReqId() == null ? 0L : reqInfo.getReqId());
        out.setSuccess(predictOk);
        out.setMessage(predictMsg);
        out.setMatchRecords(records);
        return out;
    }

    // ========== 私有方法 ==========

    private Map<String, String> safeBatchGet(String prefix, Collection<String> keys) {
        try {
            return tairClient.batchGet(prefix, keys);
        } catch (Throwable t) {
            return Collections.emptyMap();
        }
    }

    // 解析 Tair 中的 JSON，尽力提取 matchId/poiId/poi_id
    private String extractMatchId(String json) {
        if (isBlank(json)) return null;
        // 尝试常见 key
        String[] keys = new String[]{"\"matchId\"", "\"poiId\"", "\"poi_id\""};
        for (String k : keys) {
            String v = extractJsonString(json, k);
            if (!isBlank(v)) return v;
        }
        return null;
    }

    private String extractJsonString(String json, String quotedKey) {
        try {
            int idx = json.indexOf(quotedKey);
            if (idx < 0) return null;
            int colon = json.indexOf(':', idx + quotedKey.length());
            if (colon < 0) return null;
            int startQuote = json.indexOf('"', colon + 1);
            if (startQuote < 0) return null;
            int endQuote = json.indexOf('"', startQuote + 1);
            if (endQuote < 0) return null;
            return json.substring(startQuote + 1, endQuote);
        } catch (Exception ignore) {
            return null;
        }
    }

    // ES 精确匹配：按 store_id terms 精确查询，返回 storeId -> poi_id
    private Map<String, String> findExactMatchFromEs(List<PoiInfo> list) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyMap();
        }
        List<String> storeIds = list.stream()
                .map(PoiInfo::getStoreId)
                .filter(s -> !isBlank(s))
                .distinct()
                .collect(Collectors.toList());
        if (storeIds.isEmpty()) {
            return Collections.emptyMap();
        }
        try {
            PorosApiClient client = EsClient.getEsClient();
            List<FieldValue> values = storeIds.stream().map(FieldValue::of).collect(Collectors.toList());
            SearchResponse<Map> response = client.search(s -> s
                            .index("waimai-bml-match")
                            .size(storeIds.size())
                            .query(q -> q
                                    .terms(t -> t
                                            .field("store_id")
                                            .terms(tt -> tt.value(values))
                                    )
                            )
                            .source(src -> src
                                    .filter(f -> f.includes(Arrays.asList("store_id", "poi_id")))
                            ),
                    Map.class);

            Map<String, String> res = new HashMap<>();
            if (response != null && response.hits() != null && response.hits().hits() != null) {
                for (Hit<Map> hit : response.hits().hits()) {
                    Map<String, Object> src = hit.source();
                    if (src == null) { continue; }
                    Object sid = src.get("store_id");
                    Object pid = src.get("poi_id");
                    if (sid != null && pid != null) {
                        res.put(String.valueOf(sid), String.valueOf(pid));
                    }
                }
            }
            return res;
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    private Map<String, Object> poiInfoToMap(PoiInfo p) {
        Map<String, Object> m = new HashMap<>();
        // 仅映射已定义字段，按需扩展
        m.put("tempPoiId", p.getTempPoiId());
        m.put("eid", p.getEid());
        m.put("storeId", p.getStoreId());
        m.put("sdId", p.getSdId());
        m.put("sdLat", p.getSdLat());
        m.put("sdLon", p.getSdLon());
        m.put("poiLat", p.getPoiLat());
        m.put("poiLon", p.getPoiLon());
        m.put("poiName", p.getPoiName());
        m.put("poiAddress", p.getPoiAddress());
        m.put("secondLocationId", p.getSecondLocationId());
        m.put("secondLocationName", p.getSecondLocationName());
        m.put("thridLocationId", p.getThridLocationId());
        m.put("thridLocationName", p.getThridLocationName());
        m.put("saleCnt", p.getSaleCnt());
        m.put("rating", p.getRating());
        m.put("openingHours", p.getOpeningHours());
        m.put("recommendReasonsStore", p.getRecommendReasonsStore());
        m.put("manjianList", p.getManjianList());
        m.put("deliveryType", p.getDeliveryType());
        m.put("distance", p.getDistance());
        m.put("startCharge", p.getStartCharge());
        m.put("actualShipfee", p.getActualShipfee());
        m.put("noSubsidyFee", p.getNoSubsidyFee());
        m.put("subsidyFee", p.getSubsidyFee());
        m.put("firstOpenTime", p.getFirstOpenTime());
        return m;
    }

    private Map<String, Object> buildExtra(ReqInfo reqInfo) {
        Map<String, Object> extra = new HashMap<>();
        extra.put("reqId", reqInfo.getReqId());
        extra.put("type", reqInfo.getType());
        extra.put("canNewId", reqInfo.getCanNewId());
        return extra;
    }

    // 通过反射构造并调用 TPredictService#predict
    private TPredictResponse doPredictReflect(String bizCode, List<Map<String, Object>> poiFields, Map<String, String> extra) throws Exception {
        TPredictRequest tPredictRequest = new TPredictRequest();
        tPredictRequest.setBizCode(bizCode);
        // data 封装 poiFields
        Map<String, String> data = new HashMap<>();
        data.put("poiFields", JSONObject.toJSONString(poiFields));
        tPredictRequest.setReq(data);
        // extra
        tPredictRequest.setExtra(extra);

        return predictService.predict(tPredictRequest);
    }

    private void invokeSetterIfExists(Class<?> clz, Object obj, String method, Class<?> paramType, Object value) {
        try {
            Method m = clz.getMethod(method, paramType);
            m.invoke(obj, value);
        } catch (Exception ignore) {
            // 允许请求结构差异，不抛错
        }
    }

    // 解析 TPredictResponse
    private Map<String, Object> parsePredictResponse(Object resp) throws Exception {
        Map<String, Object> out = new HashMap<>();
        if (resp == null) {
            out.put("code", -1);
            out.put("message", "null response");
            return out;
        }
        Class<?> rclz = resp.getClass();
        Integer code = (Integer) invokeGetter(rclz, resp, "getCode");
        Object message = invokeGetter(rclz, resp, "getMessage");
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) invokeGetter(rclz, resp, "getData");
        if (data == null) data = Collections.emptyMap();
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> result = (List<Map<String, Object>>) data.getOrDefault("result", Collections.emptyList());
        out.put("code", code == null ? -1 : code);
        out.put("message", message == null ? "" : String.valueOf(message));
        out.put("result", result);
        return out;
    }

    private Object invokeGetter(Class<?> clz, Object obj, String getter) {
        try {
            Method m = clz.getMethod(getter);
            return m.invoke(obj);
        } catch (Exception e) {
            return null;
        }
    }

    private static boolean isBlank(String s) { return s == null || s.trim().isEmpty(); }
    private static boolean allEmpty(String a, String b) { return isBlank(a) && isBlank(b); }
    private static String firstNonBlank(String a, String b) { return !isBlank(a) ? a : b; }

    private static Object firstNonNull(Object a, Object b) { return a != null ? a : b; }
    private static String asStr(Object o) { return o == null ? null : String.valueOf(o); }
    private static Integer asInt(Object o) {
        if (o == null) return null; if (o instanceof Integer) return (Integer)o; try { return Integer.parseInt(String.valueOf(o)); } catch (Exception e) { return null; }
    }
    private static Double asDouble(Object o) {
        if (o == null) return null; if (o instanceof Double) return (Double)o; try { return Double.parseDouble(String.valueOf(o)); } catch (Exception e) { return null; }
    }
}
